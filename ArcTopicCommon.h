/*----------------------------------------------------------------------------------------------
*
* This file is ArcSoft's property. It contains ArcSoft's trade secret, proprietary and 		
* confidential information. 
* 
* The information and code contained in this file is only for authorized ArcSoft employees 
* to design, create, modify, or review.
* 
* DO NOT DISTRIBUTE, DO NOT DUPLICATE OR TRANSMIT IN ANY FORM WITHOUT PROPER AUTHORIZATION.
* 
* If you are not an intended recipient of this file, you must not copy, distribute, modify, 
* or take any action in reliance on it. 
* 
* If you have received this file in error, please immediately notify ArcSoft and 
* permanently delete the original and any copy of any file and any printout thereof.
*
*-------------------------------------------------------------------------------------------------*/
#ifndef ARC_TOPIC_COMMON_H
#define ARC_TOPIC_COMMON_H
#include <stdint.h>

#pragma pack(4)
#define TOPIC_HEADER_VERSION_SIZE 16

typedef struct {
    char strVer[TOPIC_HEADER_VERSION_SIZE];     //版本号
    uint64_t utcTimestamp;                      // 绝对时间，单位毫秒
	uint64_t bootTimestamp;                     // 相对时间，基准为上电，单调递增，单位毫秒
    uint32_t frameId;                           // 帧ID
}topic_header_t;

#pragma pack()

#endif
